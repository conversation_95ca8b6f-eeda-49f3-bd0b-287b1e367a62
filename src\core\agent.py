"""
Core AI agent for Arien AI.

This module implements the main AI agent that orchestrates LLM providers,
function tools, and execution strategies with intelligent decision-making.
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple

from src.providers.base import BaseLLMProvider, Message, MessageRole, LLMResponse
from src.providers.deepseek import DeepseekProvider
from src.providers.ollama import OllamaProvider
from src.tools.base import BaseTool, ToolResult, ToolStatus
from src.tools.bash_tool import BashTool
from src.tools.web_search_tool import WebSearchTool
from src.core.exceptions import ArienError, ConfigurationError, LLMError
from src.config.settings import Settings

logger = logging.getLogger(__name__)


@dataclass
class AgentResponse:
    """Response from the AI agent."""
    content: str
    tool_results: List['ToolExecution']
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ToolExecution:
    """Tool execution record."""
    tool_name: str
    result: ToolResult
    parameters: Dict[str, Any]


class ArienAgent:
    """
    Core AI agent that orchestrates LLM providers and function tools.
    
    Features:
    - Multi-provider LLM support (Deepseek, Ollama)
    - Intelligent tool selection and execution
    - Parallel vs sequential execution strategies
    - Comprehensive error handling and retry logic
    - User confirmation for destructive operations
    """
    
    # System prompt with comprehensive instructions
    SYSTEM_PROMPT = """You are Arien AI, a sophisticated AI-powered CLI terminal assistant. You have access to powerful function tools and can help users with a wide variety of tasks.

CORE CAPABILITIES:
You can execute bash commands, search the web for real-time information, and provide intelligent assistance for development, system administration, research, and automation tasks.

AVAILABLE TOOLS:

1. BASH TOOL - Execute shell/bash commands
   WHEN TO USE:
   - File operations (create, read, modify, delete files and directories)
   - System information gathering (ps, df, ls, find, grep, etc.)
   - Development tasks (git operations, building, testing, package management)
   - Process management and monitoring
   - Network operations (ping, curl, wget, ssh)
   - Text processing (sed, awk, sort, uniq, cut)
   - Software installation and configuration
   
   WHEN NOT TO USE:
   - For information you already know from training data
   - For simple calculations or explanations
   - For destructive operations without explicit user permission
   - For commands requiring interactive input
   
   EXECUTION STRATEGY:
   - Commands run sequentially by default for safety
   - Use parallel execution only for independent, read-only operations
   - Always validate command safety before execution
   - Provide clear explanations of what commands do
   
   SECURITY CONSIDERATIONS:
   - Dangerous operations require user confirmation
   - Validate commands for potential risks
   - Explain potential consequences of destructive operations
   - Use appropriate permissions and safety measures

2. WEB SEARCH TOOL - Search the web for real-time information
   WHEN TO USE:
   - Finding current information not in your training data
   - Researching recent events, news, or developments
   - Looking up specific facts, statistics, or current data
   - Finding documentation, tutorials, or guides
   - Checking current prices, availability, or status
   - Researching companies, products, or services
   - Getting multiple perspectives on topics
   
   WHEN NOT TO USE:
   - For information you already know from training data
   - For simple explanations or basic concepts
   - For personal or private information
   - For illegal or harmful content
   
   SEARCH STRATEGY:
   - Use specific, targeted queries for better results
   - Include relevant keywords and context
   - Try different phrasings if initial results aren't helpful
   - Combine search results with your knowledge for comprehensive answers

EXECUTION STRATEGIES:

PARALLEL vs SEQUENTIAL EXECUTION:
- Use PARALLEL execution for:
  * Independent read-only operations
  * Multiple web searches on different topics
  * Non-interfering system checks
  * Gathering information from multiple sources

- Use SEQUENTIAL execution for:
  * Operations that depend on previous results
  * File modifications that might conflict
  * System changes that could affect subsequent operations
  * Any potentially destructive operations

DECISION-MAKING PROCESS:
1. Analyze the user's request thoroughly
2. Determine which tools are needed and in what order
3. Consider dependencies between operations
4. Choose appropriate execution strategy (parallel/sequential)
5. Execute tools with proper error handling
6. Synthesize results into a comprehensive response

ERROR HANDLING:
- Retry failed operations when appropriate
- Provide clear explanations of errors
- Suggest alternative approaches when operations fail
- Always inform users of any issues or limitations

USER INTERACTION:
- Ask for confirmation before destructive operations
- Explain what you're doing and why
- Provide clear, actionable responses
- Offer alternatives when requests cannot be fulfilled safely

RESPONSE FORMAT:
- Provide clear, well-structured responses
- Explain your reasoning and approach
- Include relevant details from tool executions
- Offer follow-up suggestions when appropriate

Remember: You are a powerful assistant with access to system-level tools. Always prioritize safety, security, and user consent for potentially risky operations."""

    def __init__(self, settings: Settings) -> None:
        """
        Initialize the AI agent.

        Args:
            settings: Configuration settings
        """
        self.settings = settings
        
        # Initialize LLM provider
        self.llm_provider = self._create_llm_provider()
        
        # Initialize tools
        self.tools = self._create_tools()
        
        # Conversation history
        self.conversation_history: List[Message] = [
            Message(role=MessageRole.SYSTEM, content=self.SYSTEM_PROMPT)
        ]
    
    def _create_llm_provider(self) -> BaseLLMProvider:
        """Create LLM provider based on configuration."""
        if self.settings.llm.provider == "deepseek":
            if not self.settings.llm.api_key:
                raise ConfigurationError("Deepseek API key is required")
            
            return DeepseekProvider(
                api_key=self.settings.llm.api_key,
                model=self.settings.llm.model,
                base_url=self.settings.llm.base_url or "https://api.deepseek.com/v1",
                max_tokens=self.settings.llm.max_tokens,
                temperature=self.settings.llm.temperature,
                timeout=self.settings.llm.timeout,
            )
        
        elif self.settings.llm.provider == "ollama":
            return OllamaProvider(
                model=self.settings.llm.model,
                base_url=self.settings.llm.base_url or "http://localhost:11434",
                timeout=self.settings.llm.timeout,
            )
        
        else:
            raise ConfigurationError(f"Unsupported LLM provider: {self.settings.llm.provider}")
    
    def _create_tools(self) -> Dict[str, BaseTool]:
        """Create and initialize function tools."""
        tools = {}
        
        if self.settings.tools.bash_enabled:
            tools["bash"] = BashTool()
        
        if self.settings.tools.web_search_enabled:
            tools["web_search"] = WebSearchTool()
        
        return tools
    
    def _get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get tool schemas for LLM function calling."""
        return [tool.get_schema() for tool in self.tools.values()]
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to LLM provider.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            return await self.llm_provider.validate_connection()
        except Exception as e:
            logger.error(f"Connection validation failed: {e}")
            return False
    
    async def process_request(self, user_input: str) -> AgentResponse:
        """
        Process user request with AI agent.
        
        Args:
            user_input: User's input/request
            
        Returns:
            Agent response with content and tool results
        """
        try:
            # Add user message to conversation
            user_message = Message(role=MessageRole.USER, content=user_input)
            self.conversation_history.append(user_message)
            
            # Generate initial response
            tool_schemas = self._get_tool_schemas() if self.tools else None
            response = await self.llm_provider.generate_response(
                messages=self.conversation_history,
                tools=tool_schemas,
                max_tokens=self.settings.llm.max_tokens,
                temperature=self.settings.llm.temperature,
            )
            
            # Process tool calls if any
            tool_executions = []
            if response.tool_calls:
                tool_executions = await self._execute_tools(response.tool_calls)
                
                # Add tool results to conversation and get final response
                response = await self._process_tool_results(response, tool_executions)
            
            # Add assistant response to conversation
            assistant_message = Message(
                role=MessageRole.ASSISTANT,
                content=response.content,
                tool_calls=[{
                    "id": tc.id,
                    "type": "function",
                    "function": {"name": tc.name, "arguments": json.dumps(tc.arguments)}
                } for tc in response.tool_calls] if response.tool_calls else None
            )
            self.conversation_history.append(assistant_message)
            
            return AgentResponse(
                content=response.content,
                tool_results=tool_executions,
                metadata={
                    "model": response.model,
                    "usage": response.usage,
                    "finish_reason": response.finish_reason,
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            raise ArienError(f"Failed to process request: {str(e)}")
    
    async def _execute_tools(self, tool_calls: List) -> List[ToolExecution]:
        """
        Execute tool calls with intelligent parallel/sequential strategy.
        
        Args:
            tool_calls: List of tool calls from LLM
            
        Returns:
            List of tool execution results
        """
        tool_executions = []
        
        # Analyze tool calls for execution strategy
        execution_groups = self._analyze_execution_strategy(tool_calls)
        
        for group in execution_groups:
            if len(group) == 1:
                # Single tool execution
                execution = await self._execute_single_tool(group[0])
                tool_executions.append(execution)
            else:
                # Parallel execution
                tasks = [self._execute_single_tool(tool_call) for tool_call in group]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, Exception):
                        # Handle execution exception
                        logger.error(f"Tool execution failed: {result}")
                        # Create error result
                        execution = ToolExecution(
                            tool_name="unknown",
                            result=ToolResult(
                                status=ToolStatus.ERROR,
                                output="",
                                error=str(result)
                            ),
                            parameters={}
                        )
                    else:
                        execution = result
                    
                    tool_executions.append(execution)
        
        return tool_executions
    
    def _analyze_execution_strategy(self, tool_calls: List) -> List[List]:
        """
        Analyze tool calls and group them for optimal execution strategy.
        
        Args:
            tool_calls: List of tool calls
            
        Returns:
            List of execution groups (each group can run in parallel)
        """
        groups = []
        current_group = []
        
        for tool_call in tool_calls:
            tool_name = tool_call.name
            tool = self.tools.get(tool_name)
            
            if not tool:
                # Unknown tool, execute separately
                if current_group:
                    groups.append(current_group)
                    current_group = []
                groups.append([tool_call])
                continue
            
            # Check if tool can run in parallel
            if tool.can_run_parallel and current_group:
                # Check if all tools in current group can run in parallel
                can_add_to_group = all(
                    self.tools.get(tc.name, type('', (), {'can_run_parallel': False})).can_run_parallel
                    for tc in current_group
                )
                
                if can_add_to_group:
                    current_group.append(tool_call)
                else:
                    # Start new group
                    groups.append(current_group)
                    current_group = [tool_call]
            else:
                # Tool must run sequentially
                if current_group:
                    groups.append(current_group)
                    current_group = []
                groups.append([tool_call])
        
        # Add remaining group
        if current_group:
            groups.append(current_group)
        
        return groups
    
    async def _execute_single_tool(self, tool_call) -> ToolExecution:
        """
        Execute a single tool call.
        
        Args:
            tool_call: Tool call to execute
            
        Returns:
            Tool execution result
        """
        tool_name = tool_call.name
        tool = self.tools.get(tool_name)
        
        if not tool:
            return ToolExecution(
                tool_name=tool_name,
                result=ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Unknown tool: {tool_name}"
                ),
                parameters=tool_call.arguments
            )
        
        try:
            # Check if tool requires confirmation
            if tool.requires_confirmation and self.settings.ui.confirm_destructive:
                # Import here to avoid circular import
                from src.ui.formatter import OutputFormatter
                formatter = OutputFormatter()
                confirmation_message = f"Tool '{tool_name}' wants to execute: {tool_call.arguments}. Continue?"
                if not formatter.format_confirmation(confirmation_message):
                    return ToolExecution(
                        tool_name=tool_name,
                        result=ToolResult(
                            status=ToolStatus.CANCELLED,
                            output="",
                            error="Operation cancelled by user"
                        ),
                        parameters=tool_call.arguments
                    )
            
            # Execute tool
            result = await tool.execute(**tool_call.arguments)
            
            return ToolExecution(
                tool_name=tool_name,
                result=result,
                parameters=tool_call.arguments
            )
            
        except Exception as e:
            logger.error(f"Tool execution error: {e}")
            return ToolExecution(
                tool_name=tool_name,
                result=ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=str(e)
                ),
                parameters=tool_call.arguments
            )
    
    async def _process_tool_results(self, initial_response: LLMResponse, tool_executions: List[ToolExecution]) -> LLMResponse:
        """
        Process tool results and get final response from LLM.
        
        Args:
            initial_response: Initial LLM response with tool calls
            tool_executions: Tool execution results
            
        Returns:
            Final LLM response
        """
        # Add tool results to conversation
        for execution in tool_executions:
            tool_message = Message(
                role=MessageRole.TOOL,
                content=json.dumps({
                    "status": execution.result.status.value,
                    "output": execution.result.output,
                    "error": execution.result.error,
                    "metadata": execution.result.metadata,
                }),
                tool_call_id=f"call_{execution.tool_name}",
                name=execution.tool_name
            )
            self.conversation_history.append(tool_message)
        
        # Get final response
        final_response = await self.llm_provider.generate_response(
            messages=self.conversation_history,
            max_tokens=self.settings.llm.max_tokens,
            temperature=self.settings.llm.temperature,
        )
        
        return final_response
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        try:
            if hasattr(self.llm_provider, '__aexit__'):
                await self.llm_provider.__aexit__(None, None, None)
            
            for tool in self.tools.values():
                if hasattr(tool, '__aexit__'):
                    await tool.__aexit__(None, None, None)
                    
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
